@page "/Story/StyleStep/{id:guid}"
@model AICreator.Pages.Story.StyleStepModel
@{
    ViewData["Title"] = "Przykłady stylistyczne - Krok 1.5";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <span class="badge bg-light text-info me-2">Krok 1.5</span>
                        P<PERSON><PERSON>ła<PERSON> stylistyczne (opcjonalne)
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Podsumowanie z poprzedniego kroku -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-book me-2"></i>Gatunek</h6>
                                    <p class="card-text">@Model.Story.Genre</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-users me-2"></i>Bohaterowie</h6>
                                    <p class="card-text">@Model.Story.Characters</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-map me-2"></i>Zarys</h6>
                                    <p class="card-text">@Model.Story.PlotOutline</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Główna sekcja z przykładami stylistycznymi -->
                    <div class="mb-4">
                        <h5 class="mb-3">
                            <i class="fas fa-palette me-2"></i>Przykłady stylistyczne
                        </h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Opcjonalne:</strong> Podaj przykładowe fragmenty tekstów, które mają inspirować AI w kwestii stylu pisania, słownictwa, długości zdań, ilości dialogów itp. 
                            Możesz wkleić fragmenty z ulubionych książek, własnych tekstów lub opisać pożądany styl słowami.
                        </div>

                        <form method="post">
                            <input type="hidden" asp-for="Story.Id" />
                            <input type="hidden" asp-for="Story.Genre" />
                            <input type="hidden" asp-for="Story.Characters" />
                            <input type="hidden" asp-for="Story.PlotOutline" />

                            <div class="mb-4">
                                <label asp-for="Story.StyleExamples" class="form-label">
                                    <i class="fas fa-feather-alt me-2"></i>Przykłady stylistyczne
                                </label>
                                <textarea asp-for="Story.StyleExamples" 
                                          class="form-control" 
                                          rows="12" 
                                          placeholder="Przykład 1: Fragment tekstu pokazujący pożądany styl...

Przykład 2: Inny fragment lub opis stylu...

Możesz opisać:
- Długość zdań (krótkie/długie)
- Styl narracji (pierwsza/trzecia osoba)
- Ilość dialogów
- Poziom szczegółowości opisów
- Ton (poważny/lekki/humorystyczny)
- Słownictwo (proste/bogate/specjalistyczne)"></textarea>
                                <div class="form-text">
                                    Maksymalnie 5000 znaków. AI będzie starał się naśladować podany styl podczas generowania treści.
                                </div>
                            </div>

                            <!-- Przykłady gotowych stylów -->
                            <div class="mb-4">
                                <h6 class="mb-3">
                                    <i class="fas fa-lightbulb me-2"></i>Gotowe przykłady stylów
                                </h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="card style-example-card" onclick="setStyleExample('classic')">
                                            <div class="card-body">
                                                <h6 class="card-title">📚 Klasyczny</h6>
                                                <p class="card-text small">Długie, rozbudowane zdania. Bogate opisy. Formalna narracja.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card style-example-card" onclick="setStyleExample('modern')">
                                            <div class="card-body">
                                                <h6 class="card-title">⚡ Współczesny</h6>
                                                <p class="card-text small">Krótkie zdania. Dynamiczna akcja. Dużo dialogów.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card style-example-card" onclick="setStyleExample('descriptive')">
                                            <div class="card-body">
                                                <h6 class="card-title">🎨 Opisowy</h6>
                                                <p class="card-text small">Szczegółowe opisy miejsc i postaci. Atmosferyczne.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card style-example-card" onclick="setStyleExample('dialogue')">
                                            <div class="card-body">
                                                <h6 class="card-title">💬 Dialogowy</h6>
                                                <p class="card-text small">Głównie dialogi. Minimalne opisy. Szybka wymiana kwestii.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Przyciski nawigacji -->
                            <div class="d-flex justify-content-between">
                                <a href="/Story/Edit/@Model.Story.Id" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Wróć do kroku 1
                                </a>
                                <div>
                                    <button type="submit" asp-page-handler="SkipStyle" class="btn btn-outline-primary me-2">
                                        <i class="fas fa-forward me-2"></i>Pomiń ten krok
                                    </button>
                                    <button type="submit" asp-page-handler="SaveStyle" class="btn btn-success">
                                        <i class="fas fa-arrow-right me-2"></i>Zapisz i przejdź dalej
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        const styleExamples = {
            classic: `Styl klasyczny - długie, rozbudowane zdania z bogatym słownictwem:

"Słońce, które przez całe popołudnie skrywało się za ciężkimi, ołowianymi chmurami, w końcu przebijało się przez ich szare zasłony, rzucając złociste promienie na mokrą od deszczu ziemię. Powietrze było świeże i czyste, niosąc ze sobą zapach wilgotnej gleby i kwitnących lip, które rosły wzdłuż kamiennej ścieżki prowadzącej do starego dworu."

Cechy: długie zdania, bogate epitety, szczegółowe opisy przyrody i miejsc.`,

            modern: `Styl współczesny - krótkie zdania, dynamiczna akcja:

"Telefon zadzwonił. Sarah sięgnęła po słuchawkę.
- Halo?
- To ja. Musimy się spotkać.
- Teraz?
- Tak. To pilne.
Rozłączyła się. Serce biło jej szybciej. Coś było nie tak."

Cechy: krótkie zdania, dużo dialogów, szybkie tempo, minimalne opisy.`,

            descriptive: `Styl opisowy - szczegółowe charakterystyki:

"Pokój tonął w półmroku. Ciężkie, bordowe zasłony zasłaniały okna, przepuszczając jedynie wąskie pasma światła, które tańczyły na zakurzonych meblach. W kącie stał stary sekretarzyk z orzechowego drewna, jego powierzchnia pokryta była drobnymi rysami - śladami lat użytkowania. Na biurku leżały pożółkłe listy, związane wyblakłą wstążką."

Cechy: bardzo szczegółowe opisy, atmosferyczne, mało dialogów, skupienie na detalach.`,

            dialogue: `Styl dialogowy - głównie rozmowy:

"- Gdzie byłeś wczoraj wieczorem?
- W domu. Oglądałem film.
- Jaki film?
- Nie pamiętam tytułu.
- Dziwne. Dzwoniłem do ciebie. Nikt nie odbierał.
- Może spałem.
- O ósmej wieczorem?
- Jestem ostatnio zmęczony.
- Kłamiesz."

Cechy: głównie dialogi, minimalne opisy akcji, szybka wymiana kwestii, napięcie w rozmowach.`
        };

        function setStyleExample(styleType) {
            const textarea = document.getElementById('Story_StyleExamples');
            textarea.value = styleExamples[styleType];
            
            // Podświetl wybraną kartę
            document.querySelectorAll('.style-example-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
        }
    </script>
}
