using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using AICreator.Data;
using AICreator.Models;
using AICreator.Services;
using System.Text.Json;

namespace AICreator.Pages.Story
{
    public class Step2Model : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly OpenRouterService _openRouterService;
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        public Step2Model(ApplicationDbContext context, OpenRouterService openRouterService)
        {
            _context = context;
            _openRouterService = openRouterService;
        }

        [BindProperty]
        public Models.Story Story { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(Guid id)
        {
            var story = await _context.Stories
                .Select(s => new Models.Story
                {
                    Id = s.Id,
                    Genre = s.Genre,
                    Characters = s.Characters,
                    PlotOutline = s.PlotOutline,
                    StyleExamples = s.StyleExamples,
                    StoryStructureJson = s.StoryStructureJson,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.UpdatedAt,
                    Content = s.Content,
                    IsCompleted = s.IsCompleted
                })
                .FirstOrDefaultAsync(m => m.Id == id);

            if (story == null)
            {
                return NotFound();
            }

            Story = story;
            return Page();
        }

        public async Task<IActionResult> OnPostRegenerateOutlineAsync()
        {
            var storyToUpdate = await _context.Stories.FindAsync(Story.Id);
            if (storyToUpdate == null)
            {
                return NotFound();
            }

            try
            {
                var outline = await _openRouterService.GenerateStoryOutlineAsync(
                    storyToUpdate.Genre ?? "",
                    storyToUpdate.Characters ?? "",
                    storyToUpdate.PlotOutline ?? "");

                // Konwertuj wygenerowany tekst na listę punktów
                var points = outline.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                                   .Select(line => line.Trim())
                                   .Where(line => !string.IsNullOrEmpty(line))
                                   .ToList();

                storyToUpdate.OutlinePoints = points;
                storyToUpdate.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                storyToUpdate.OutlinePoints = new List<string> { $"Błąd generowania konspektu: {ex.Message}" };
                await _context.SaveChangesAsync();
            }

            return RedirectToPage("./Step2", new { id = Story.Id });
        }

        public async Task<IActionResult> OnPostUpdateItemAsync()
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            Console.WriteLine($"Received body: {body}");

            var data = JsonSerializer.Deserialize<UpdateItemRequest>(body, JsonOptions);
            Console.WriteLine($"Deserialized data: Index={data?.Index}, Content={data?.Content}");

            if (data == null)
            {
                Console.WriteLine("Data is null, returning BadRequest");
                return BadRequest("Invalid data");
            }

            var story = await _context.Stories.FindAsync(Story.Id);
            if (story == null) return NotFound();

            var points = story.OutlinePoints;

            if (data.Index >= 0 && data.Index < points.Count)
            {
                points[data.Index] = data.Content;
                story.OutlinePoints = points;
                story.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            return new JsonResult(new { success = true });
        }

        public async Task<IActionResult> OnPostAddItemAsync()
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            var data = JsonSerializer.Deserialize<AddItemRequest>(body, JsonOptions);

            if (data == null) return BadRequest();

            var story = await _context.Stories.FindAsync(Story.Id);
            if (story == null) return NotFound();

            var points = story.OutlinePoints;

            if (data.Index >= 0 && data.Index <= points.Count)
            {
                points.Insert(data.Index, data.Content);
                story.OutlinePoints = points;
                story.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            return new JsonResult(new { success = true });
        }

        public async Task<IActionResult> OnPostDeleteItemAsync()
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            var data = JsonSerializer.Deserialize<DeleteItemRequest>(body, JsonOptions);

            if (data == null) return BadRequest();

            var story = await _context.Stories.FindAsync(Story.Id);
            if (story == null) return NotFound();

            var points = story.OutlinePoints;

            if (data.Index >= 0 && data.Index < points.Count)
            {
                points.RemoveAt(data.Index);
                story.OutlinePoints = points;
                story.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            return new JsonResult(new { success = true });
        }

        public async Task<IActionResult> OnPostGenerateParagraphAsync()
        {
            using var reader = new StreamReader(Request.Body);
            var body = await reader.ReadToEndAsync();
            var data = JsonSerializer.Deserialize<GenerateParagraphRequest>(body, JsonOptions);

            if (data == null) return BadRequest();

            var story = await _context.Stories.FindAsync(Story.Id);
            if (story == null) return NotFound();

            var outlinePoints = story.OutlinePoints;
            var generatedParagraphs = story.GeneratedParagraphs;

            // Sprawdź czy indeks jest prawidłowy
            if (data.Index < 0 || data.Index >= outlinePoints.Count)
                return BadRequest("Invalid index");

            // Sprawdź czy poprzedni paragraf został wygenerowany (jeśli nie jest to pierwszy punkt)
            if (data.Index > 0 && (generatedParagraphs.Count <= data.Index - 1 || string.IsNullOrEmpty(generatedParagraphs[data.Index - 1])))
                return BadRequest("Previous paragraph must be generated first");

            try
            {
                // Przygotuj kontekst z poprzednich paragrafów
                string? previousContext = null;
                if (data.Index > 0 && generatedParagraphs.Count > data.Index - 1)
                {
                    var previousParagraph = generatedParagraphs[data.Index - 1];
                    if (!string.IsNullOrEmpty(previousParagraph))
                    {
                        // Weź ostatnie 2-3 zdania z poprzedniego paragrafu
                        var sentences = previousParagraph.Split('.', StringSplitOptions.RemoveEmptyEntries);
                        if (sentences.Length > 0)
                        {
                            var lastSentences = sentences.TakeLast(Math.Min(3, sentences.Length));
                            previousContext = string.Join(". ", lastSentences) + ".";
                        }
                    }
                }

                // Generuj paragraf
                var paragraph = await _openRouterService.GenerateParagraphAsync(
                    story.Genre ?? "",
                    story.Characters ?? "",
                    story.PlotOutline ?? "",
                    outlinePoints[data.Index],
                    previousContext,
                    story.StyleExamples);

                // Upewnij się, że lista ma odpowiednią długość
                while (generatedParagraphs.Count <= data.Index)
                {
                    generatedParagraphs.Add("");
                }

                // Zapisz wygenerowany paragraf
                generatedParagraphs[data.Index] = paragraph;
                story.GeneratedParagraphs = generatedParagraphs;
                story.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                return new JsonResult(new { success = true, paragraph = paragraph });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, error = ex.Message });
            }
        }
    }

    public class UpdateItemRequest
    {
        public int Index { get; set; }
        public string Content { get; set; } = string.Empty;
    }

    public class AddItemRequest
    {
        public int Index { get; set; }
        public string Content { get; set; } = string.Empty;
    }

    public class DeleteItemRequest
    {
        public int Index { get; set; }
    }

    public class GenerateParagraphRequest
    {
        public int Index { get; set; }
    }
}
