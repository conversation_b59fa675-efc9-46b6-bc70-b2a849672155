using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using AICreator.Data;
using AICreator.Models;
using AICreator.Services;

namespace AICreator.Pages.Story
{
    public class StyleStepModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly OpenRouterService _openRouterService;

        public StyleStepModel(ApplicationDbContext context, OpenRouterService openRouterService)
        {
            _context = context;
            _openRouterService = openRouterService;
        }

        [BindProperty]
        public Models.Story Story { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(Guid id)
        {
            var story = await _context.Stories.FirstOrDefaultAsync(m => m.Id == id);

            if (story == null)
            {
                return NotFound();
            }

            Story = story;
            return Page();
        }

        public async Task<IActionResult> OnPostSaveStyleAsync()
        {
            var storyToUpdate = await _context.Stories.FindAsync(Story.Id);
            if (storyToUpdate == null)
            {
                return NotFound();
            }

            storyToUpdate.StyleExamples = Story.StyleExamples;
            storyToUpdate.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Generuj konspekt za pomocą OpenRouter z uwzględnieniem stylu
            try
            {
                var outline = await _openRouterService.GenerateStoryOutlineAsync(
                    storyToUpdate.Genre ?? "",
                    storyToUpdate.Characters ?? "",
                    storyToUpdate.PlotOutline ?? "");

                // Konwertuj wygenerowany tekst na listę punktów
                var points = outline.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                                   .Select(line => line.Trim())
                                   .Where(line => !string.IsNullOrEmpty(line))
                                   .ToList();

                storyToUpdate.OutlinePoints = points;
                await _context.SaveChangesAsync();

                return RedirectToPage("./Step2", new { id = Story.Id });
            }
            catch (Exception ex)
            {
                // W przypadku błędu, zapisz informację i przekieruj do kroku 2
                storyToUpdate.OutlinePoints = new List<string> { $"Błąd generowania konspektu: {ex.Message}" };
                await _context.SaveChangesAsync();
                return RedirectToPage("./Step2", new { id = Story.Id });
            }
        }

        public async Task<IActionResult> OnPostSkipStyleAsync()
        {
            var storyToUpdate = await _context.Stories.FindAsync(Story.Id);
            if (storyToUpdate == null)
            {
                return NotFound();
            }

            // Generuj konspekt bez przykładów stylistycznych
            try
            {
                var outline = await _openRouterService.GenerateStoryOutlineAsync(
                    storyToUpdate.Genre ?? "",
                    storyToUpdate.Characters ?? "",
                    storyToUpdate.PlotOutline ?? "");

                var points = outline.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                                   .Select(line => line.Trim())
                                   .Where(line => !string.IsNullOrEmpty(line))
                                   .ToList();

                storyToUpdate.OutlinePoints = points;
                await _context.SaveChangesAsync();

                return RedirectToPage("./Step2", new { id = Story.Id });
            }
            catch (Exception ex)
            {
                storyToUpdate.OutlinePoints = new List<string> { $"Błąd generowania konspektu: {ex.Message}" };
                await _context.SaveChangesAsync();
                return RedirectToPage("./Step2", new { id = Story.Id });
            }
        }
    }
}
