﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AICreator.Migrations
{
    /// <inheritdoc />
    public partial class CombineOutlineAndParagraphs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GeneratedOutlineJson",
                table: "Stories");

            migrationBuilder.RenameColumn(
                name: "GeneratedParagraphsJson",
                table: "Stories",
                newName: "StoryStructureJson");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "StoryStructure<PERSON><PERSON>",
                table: "Stories",
                newName: "GeneratedParagraphsJson");

            migrationBuilder.AddColumn<string>(
                name: "GeneratedOutlineJson",
                table: "Stories",
                type: "TEXT",
                nullable: true);
        }
    }
}
