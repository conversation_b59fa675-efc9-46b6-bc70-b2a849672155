using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace AICreator.Models
{
    public class StorySection
    {
        public string Outline { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
    }
    public class Story
    {
        public Guid Id { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        // Krok 1: Podstawowe informacje
        [MaxLength(100)]
        public string? Genre { get; set; }

        [MaxLength(1000)]
        public string? Characters { get; set; }

        [MaxLength(2000)]
        public string? PlotOutline { get; set; }

        // Krok 1.5: Przykładowe teksty stylistyczne
        [MaxLength(5000)]
        public string? StyleExamples { get; set; }

        // Krok 2: Wygenerowany konspekt i paragrafy jako JSON
        public string? StoryStructureJson { get; set; }

        // Helper property dla łatwego dostępu do struktury opowiadania
        [NotMapped]
        public List<StorySection> StoryStructure
        {
            get
            {
                if (string.IsNullOrEmpty(StoryStructureJson))
                    return new List<StorySection>();

                try
                {
                    return JsonSerializer.Deserialize<List<StorySection>>(StoryStructureJson) ?? new List<StorySection>();
                }
                catch
                {
                    return new List<StorySection>();
                }
            }
            set
            {
                StoryStructureJson = JsonSerializer.Serialize(value);
            }
        }

        // Helper properties dla kompatybilności wstecznej
        [NotMapped]
        public List<string> OutlinePoints
        {
            get => StoryStructure.Select(s => s.Outline).ToList();
            set
            {
                var structure = new List<StorySection>();
                for (int i = 0; i < value.Count; i++)
                {
                    var existingContent = i < StoryStructure.Count ? StoryStructure[i].Content : string.Empty;
                    structure.Add(new StorySection { Outline = value[i], Content = existingContent });
                }
                StoryStructure = structure;
            }
        }

        [NotMapped]
        public List<string> GeneratedParagraphs
        {
            get => StoryStructure.Select(s => s.Content).ToList();
            set
            {
                var structure = new List<StorySection>();
                for (int i = 0; i < value.Count; i++)
                {
                    var existingOutline = i < StoryStructure.Count ? StoryStructure[i].Outline : string.Empty;
                    structure.Add(new StorySection { Outline = existingOutline, Content = value[i] });
                }
                StoryStructure = structure;
            }
        }

        // Wygenerowana treść
        public string Content { get; set; } = string.Empty;

        public bool IsCompleted { get; set; } = false;
    }
}
